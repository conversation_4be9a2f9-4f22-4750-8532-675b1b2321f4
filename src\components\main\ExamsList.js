import react from "react";
import Header from "../Header";
import DesktopMenu from "../DesktopMenu";
import Loader from "../Loader";
import sendpush from "../admin/sendNotification";
import axios from "axios";
import <PERSON><PERSON> from "js-cookie";
import Button from "@mui/material/Button";
import { Link } from "react-router-dom";
import commonData from "../../importanValue";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import { PDFDocument, StandardFonts, rgb } from "pdf-lib";
// import topImg from "../../pdf_top.jpeg";
import "./styles.css";
import {
  NotificationManager,
  NotificationContainer,
} from "react-notifications";
import "react-notifications/lib/notifications.css";
import { Accordion, AccordionDetails, AccordionSummary, Dialog, DialogTitle, DialogContent, DialogActions } from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import QuizIcon from '@mui/icons-material/Quiz';
import VideoLibraryIcon from '@mui/icons-material/VideoLibrary';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import React from "react";
import CryptoJS from "crypto-js";
import videojs from 'video.js';
import 'video.js/dist/video-js.css';
import '@videojs/http-streaming';

// PDF generation using html2canvas and jsPDF

// MovingWatermark component removed as per requirements

class ExamsList extends react.Component {
  // Encryption key - should match the one used in admin side
  encryptionKey = "NcExams2024SecretKey!@#";

  // Decrypt function for notes content
  decryptContent = (encryptedText) => {
    try {
      if (!encryptedText || encryptedText.trim() === '') {
        return '';
      }
      
      const decrypted = CryptoJS.AES.decrypt(encryptedText, this.encryptionKey).toString(CryptoJS.enc.Utf8);
      
      if (decrypted && decrypted.trim() !== '') {
        return decrypted;
      } else {
        // If decryption resulted in empty string, it might not be encrypted content
        return encryptedText;
      }
    } catch (error) {
      console.error('Decryption failed:', error);
      return encryptedText; // Return original text if decryption fails
    }
  };

  // Check if content is encrypted notes
  isEncryptedNotes = (content) => {
    if (!content || content.trim() === '') return false;
    
    // Check if it's a URL
    const urlPattern = /^https?:\/\/|^www\.|^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/;
    if (urlPattern.test(content.trim())) {
      return false;
    }
    
    // Try to decrypt and see if it gives meaningful content
    try {
      const decrypted = this.decryptContent(content);
      return decrypted !== content && decrypted.trim() !== '';
    } catch (error) {
      return false;
    }
  };

  state = {
    isLoading: true,
    data: [],
    groupName: "",
    expiryDate: "",
    userDetails: {},
    scheduleLink: "",
    isPDFPackage: false,
    isVideosPackage: false,
    whatsapplink: "",
    launchDates: [],
    allowNegativeMarking: false,
    only_for_practice: false,
    playingVideos: {},
    videoCredentials: {},
    loadingVideos: {},
    videoErrors: {},
    fullscreenVideo: null, // Track which video is in fullscreen
    // Notes popup state
    notesPopupOpen: false,
    notesContent: '',
    notesTitle: '',
    // VideoJS players
    videoPlayers: {}, // Store VideoJS player instances
  };

  componentDidMount() {
    this.getData();
    const { location } = this.props;
    const { search } = location;
    if (search !== "") {
      NotificationManager.success("Your Package Activated Successfully..");
    }

    // Add escape key handler for fullscreen
    this.handleEscapeKey = (e) => {
      if (e.key === 'Escape' && this.state.fullscreenVideo) {
        this.exitFullscreen();
      }
    };
    
    // Add security measures to prevent screenshots and dev tools
    this.handleSecurityKeys = (e) => {
      // Prevent PrintScreen
      if (e.key === 'PrintScreen') {
        e.preventDefault();
        NotificationManager.warning("Screenshots are not allowed");
        return false;
      }
      
      // Prevent F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U
      if (e.key === 'F12' || 
          (e.ctrlKey && e.shiftKey && (e.key === 'I' || e.key === 'J' || e.key === 'C')) ||
          (e.ctrlKey && e.key === 'U')) {
        e.preventDefault();
        NotificationManager.warning("Developer tools are disabled");
        return false;
      }
      
      // Prevent Ctrl+S (Save page)
      if (e.ctrlKey && e.key === 'S') {
        e.preventDefault();
        NotificationManager.warning("Saving page is not allowed");
        return false;
      }
      
      // Prevent Ctrl+P (Print)
      if (e.ctrlKey && e.key === 'P') {
        e.preventDefault();
        NotificationManager.warning("Printing is not allowed");
        return false;
      }
      
      // Prevent Ctrl+A (Select all)
      if (e.ctrlKey && e.key === 'A') {
        e.preventDefault();
        return false;
      }
      
      // Prevent Ctrl+C (Copy)
      if (e.ctrlKey && e.key === 'C') {
        e.preventDefault();
        return false;
      }
    };
    
    // Disable right-click context menu
    this.handleContextMenu = (e) => {
      e.preventDefault();
      return false;
    };
    
    // Handle responsive design updates
    this.handleResize = () => {
      // Force re-render when window is resized to update responsive elements
      if (this.state.notesPopupOpen) {
        // Small delay to ensure the resize is complete
        setTimeout(() => {
          this.forceUpdate();
        }, 100);
      }
    };
    
    document.addEventListener('keydown', this.handleEscapeKey);
    document.addEventListener('keydown', this.handleSecurityKeys);
    document.addEventListener('contextmenu', this.handleContextMenu);
    window.addEventListener('resize', this.handleResize);
    window.addEventListener('orientationchange', this.handleResize);
  }

  componentWillUnmount() {
    // Clean up event listeners
    document.removeEventListener('keydown', this.handleEscapeKey);
    document.removeEventListener('keydown', this.handleSecurityKeys);
    document.removeEventListener('contextmenu', this.handleContextMenu);
    window.removeEventListener('resize', this.handleResize);
    window.removeEventListener('orientationchange', this.handleResize);
    document.removeEventListener('fullscreenchange', this.handleFullscreenChange);
    document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange);
    document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange);
    document.removeEventListener('MSFullscreenChange', this.handleFullscreenChange);

    // Clean up fullscreen security if active
    this.removeFullscreenSecurity();

    // Dispose all VideoJS players
    const { videoPlayers } = this.state;
    Object.keys(videoPlayers).forEach(videoId => {
      this.disposeVideoPlayer(videoId);
    });
  }

  getData = async () => {
    const { match } = this.props;
    const { params } = match;
    const { guid, bucketId } = params;
    // console.log(window.atob(bucketId));
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    try {
      const uid = localStorage.getItem("num");
      const userDetails = await axios.post(
        `${commonData["api"]}/get-user-details/${uid}`,
        {
          headers,
        }
      );
      const quizzesList = await axios.post(
        `${commonData["api"]}/get-group-quizzes/${guid + "$$_" + window.atob(bucketId)
        }`,
        {},

        {
          headers,
        }
      );
      const { location } = this.props;
      const { search } = location;
      if (search !== "") {
        // await sendpush({
        //   title: `Hai ${userDetails.data.result[0].first_name.slice(0, 1) +
        //     userDetails.data.result[0].first_name.slice(1).toLowerCase()
        //     }.....`,
        //   message: `${quizzesList.data[0][0].group_name} Package Activated Successfully..`,
        //   filters: [
        //     { field: "tag", key: "number", relation: "=", value: String(uid) },
        //   ],
        //   url: `/exams-list/${guid}`,
        //   web_buttons: [
        //     {
        //       id: "like-button",
        //       text: " ➤ Go to Exams List",
        //       url: `/exams-list/${guid}`,
        //     },
        //   ],
        // });
      }

      const dataDaywise = quizzesList.data.slice(2)[0].reduce((acc, item) => {
        const day = item.day !== null ? item.day : "0";

        if (!acc[day]) {
          acc[day] = [];
        }

        acc[day].push(item);

        return acc;
      }, {});
      // Sort dataDaywise by keys (days)
      const sortedDataDaywise = Object.keys(dataDaywise)
        .sort((a, b) => parseInt(a, 10) - parseInt(b, 10))
        .reduce((acc, key) => {
          acc[key] = dataDaywise[key];
          return acc;
        }, {});
      this.setState({
        groupName: quizzesList.data[0][0].group_name,
        allowNegativeMarking:
          quizzesList.data[0][0].allowNegativeMarking == "1",
        only_for_practice: quizzesList.data[0][0].only_for_practice == "1",
        scheduleLink: quizzesList.data[0][0].link,
        isLoading: false,
        userDetails: userDetails.data.result[0],
        data: sortedDataDaywise,
        expiryDate: quizzesList.data.slice(1)[0][0],
        isPDFPackage:
          quizzesList.data[0][0].isNormalPackage === "1" ? false : true,
        isVideosPackage:
          quizzesList.data[0][0].isNormalPackage === "3" ? true : false,
        whatsapplink: quizzesList.data[0][0].whatsapplink,
        launchDates:
          quizzesList.data[3].length > 0
            ? quizzesList.data[3]
            : [
              {
                idlaunch_packages: 1,
                date: "2000-05-20",
                createdOn: "2000-05-20",
                gid: "175",
              },
            ],
      });
    } catch (err) {
      Cookie.remove("jwt_token");
    }
  };
  calculateApplicableLaunch(paymentDate, launchDates) {
    let applicableLaunch = null;

    for (let i = launchDates.length - 1; i >= 0; i--) {
      const launch = launchDates[i];
      if (paymentDate >= launch.createdOn) {
        applicableLaunch = launch;
        break;
      }
    }

    return applicableLaunch;
  }
  // Add method to fetch secure video credentials with signed URLs
  fetchSecureVideoCredentials = async (videoId) => {
    const token = Cookie.get("jwt_token");
    const { userDetails } = this.state;

    if (!userDetails || !userDetails.contact_no) {
      throw new Error("User phone number not available");
    }

    // console.log("Fetching video with phone:", userDetails.contact_no); // Debug log

    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };

    try {
      const response = await axios.get(
        `${commonData["api"]}/get-secure-video/${videoId}?phone=${encodeURIComponent(userDetails.contact_no)}`,
        { headers }
      );

      if (response.data.success) {
        return response.data.data;
      } else {
        console.error("Video credentials error:", response.data); // Debug log
        throw new Error(response.data.error || "Failed to fetch secure video credentials");
      }
    } catch (error) {
      console.error("Error fetching secure video credentials:", error);
      throw error;
    }
  };

  // Method to toggle video player with security check
  toggleVideoPlayer = async (videoId, quizId) => {
    const { playingVideos, loadingVideos, videoCredentials } = this.state;

    // If video is currently playing, just hide it
    if (playingVideos[quizId]) {
      this.setState(prevState => ({
        playingVideos: {
          ...prevState.playingVideos,
          [quizId]: false
        }
      }));
      return;
    }

    // Set loading state
    this.setState(prevState => ({
      loadingVideos: {
        ...prevState.loadingVideos,
        [quizId]: true
      },
      videoErrors: {
        ...prevState.videoErrors,
        [quizId]: null
      }
    }));

    try {
      // Always fetch fresh credentials to avoid token expiry issues
      const credentials = await this.fetchSecureVideoCredentials(videoId);

      // Check if we have the secure iframe URL
      if (!credentials.secureIframeUrl) {
        throw new Error("Video URL not available");
      }

      // Store credentials and show video
      this.setState(prevState => ({
        videoCredentials: {
          ...prevState.videoCredentials,
          [videoId]: credentials
        },
        playingVideos: {
          ...prevState.playingVideos,
          [quizId]: true
        },
        loadingVideos: {
          ...prevState.loadingVideos,
          [quizId]: false
        }
      }));

    } catch (error) {
      console.error("Video player error:", error);
      // Handle error with more specific messages
      const errorMessage = error.response?.data?.details || error.message || "Failed to load video";

      this.setState(prevState => ({
        loadingVideos: {
          ...prevState.loadingVideos,
          [quizId]: false
        },
        videoErrors: {
          ...prevState.videoErrors,
          [quizId]: errorMessage
        }
      }));

      NotificationManager.error(errorMessage);
    }
  };

  // Method to get secure video URL for VideoJS (convert iframe URL to direct video URL)
  getSecureVideoUrl = (videoId) => {
    const { videoCredentials } = this.state;
    const credentials = videoCredentials[videoId];

    if (credentials && credentials.secureIframeUrl) {
      // Convert iframe URL to direct video URL for VideoJS
      return this.convertIframeUrlToVideoUrl(credentials.secureIframeUrl);
    }

    return null;
  };

  // Convert iframe URL to direct video URL for VideoJS
  convertIframeUrlToVideoUrl = (iframeUrl) => {
    try {
      // Handle Cloudflare Stream URLs with signed tokens
      if (iframeUrl.includes('cloudflarestream.com') && iframeUrl.includes('/iframe')) {
        // Replace /iframe with /manifest/video.m3u8 for VideoJS HLS streaming
        const videoUrl = iframeUrl.replace('/iframe', '/manifest/video.m3u8');
        // console.log('Converted Cloudflare Stream URL:', videoUrl);
        return videoUrl;
      }

      // Handle legacy Cloudflare Stream URLs (videodelivery.net format)
      if (iframeUrl.includes('iframe.cloudflarestream.com')) {
        // Extract video ID from iframe URL
        const videoIdMatch = iframeUrl.match(/iframe\.cloudflarestream\.com\/([a-zA-Z0-9]+)/);
        if (videoIdMatch && videoIdMatch[1]) {
          const videoId = videoIdMatch[1];
          // Return HLS manifest URL for VideoJS
          return `https://videodelivery.net/${videoId}/manifest/video.m3u8`;
        }
      }

      // Handle other video platforms or direct URLs
      if (iframeUrl.includes('youtube.com') || iframeUrl.includes('youtu.be')) {
        // For YouTube, we might need to use a different approach
        // For now, return the original URL (VideoJS can handle some YouTube URLs)
        return iframeUrl;
      }

      // If it's already a direct video URL, return as is
      if (iframeUrl.includes('.mp4') || iframeUrl.includes('.m3u8') || iframeUrl.includes('.mpd')) {
        return iframeUrl;
      }

      // Fallback: return original URL
      return iframeUrl;
    } catch (error) {
      console.error('Error converting iframe URL to video URL:', error);
      return iframeUrl;
    }
  };

  // Method to get fullscreen video URL with autoplay
  getFullscreenVideoUrl = (videoId) => {
    const baseUrl = this.getSecureVideoUrl(videoId);
    if (!baseUrl) return null;

    // For HLS streams, autoplay will be handled by VideoJS player options
    return baseUrl;
  };

  // Initialize VideoJS player
  initializeVideoPlayer = (videoElement, videoId, options = {}) => {
    if (!videoElement) return null;

    try {
      const defaultOptions = {
        controls: true,
        responsive: true,
        fluid: true,
        playbackRates: [0.5, 1, 1.25, 1.5, 2],
        preload: 'metadata',
        html5: {
          hls: {
            enableLowInitialPlaylist: true,
            smoothQualityChange: true,
            overrideNative: true
          }
        },
        ...options
      };

      const player = videojs(videoElement, defaultOptions);

      // Store player reference
      this.setState(prevState => ({
        videoPlayers: {
          ...prevState.videoPlayers,
          [videoId]: player
        }
      }));

      // Add error handling
      player.on('error', (error) => {
        console.error('VideoJS player error:', error);
        this.setState(prevState => ({
          videoErrors: {
            ...prevState.videoErrors,
            [videoId]: 'Failed to load video. Please try refreshing the page.'
          }
        }));
      });

      // Add security measures
      player.on('contextmenu', (e) => {
        e.preventDefault();
        return false;
      });

      return player;
    } catch (error) {
      console.error('Error initializing VideoJS player:', error);
      return null;
    }
  };

  // Dispose VideoJS player
  disposeVideoPlayer = (videoId) => {
    const { videoPlayers } = this.state;
    const player = videoPlayers[videoId];

    if (player && typeof player.dispose === 'function') {
      try {
        player.dispose();
      } catch (error) {
        console.error('Error disposing VideoJS player:', error);
      }
    }

    // Remove player reference
    this.setState(prevState => ({
      videoPlayers: {
        ...prevState.videoPlayers,
        [videoId]: null
      }
    }));
  };

  // Custom fullscreen methods
  enterFullscreen = (videoId) => {
    // Check if video is loaded before entering fullscreen
    if (!this.getSecureVideoUrl(videoId)) {
      console.error('Cannot enter fullscreen: video not loaded');
      return;
    }

    // Find and pause the original VideoJS player
    const { videoPlayers } = this.state;
    const originalPlayer = videoPlayers[videoId];

    if (originalPlayer && typeof originalPlayer.pause === 'function') {
      try {
        originalPlayer.pause();
        // console.log('Paused original VideoJS player');
      } catch (error) {
        console.log('Could not pause original VideoJS player:', error);
      }
    }

    console.log('Attempting to enter fullscreen for video with ID:', videoId);

    // Set fullscreen state and request fullscreen
    this.setState({ fullscreenVideo: videoId });

    // Request fullscreen for the document body
    const docElement = document.documentElement;

    if (docElement.requestFullscreen) {
      docElement.requestFullscreen();
    } else if (docElement.webkitRequestFullscreen) { // Safari
      docElement.webkitRequestFullscreen();
    } else if (docElement.mozRequestFullScreen) { // Firefox
      docElement.mozRequestFullScreen();
    } else if (docElement.msRequestFullscreen) { // IE/Edge
      docElement.msRequestFullscreen();
    }

    // Add event listeners for fullscreen changes
    document.addEventListener('fullscreenchange', this.handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', this.handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', this.handleFullscreenChange);
  };

  exitFullscreen = () => {
    const currentVideoId = this.state.fullscreenVideo;
    this.setState({ fullscreenVideo: null });

    // Dispose fullscreen player
    if (this.fullscreenPlayer && typeof this.fullscreenPlayer.dispose === 'function') {
      try {
        this.fullscreenPlayer.dispose();
        this.fullscreenPlayer = null;
      } catch (error) {
        console.error('Error disposing fullscreen player:', error);
      }
    }

    if (document.exitFullscreen) {
      document.exitFullscreen();
    } else if (document.webkitExitFullscreen) { // Safari
      document.webkitExitFullscreen();
    } else if (document.mozCancelFullScreen) { // Firefox
      document.mozCancelFullScreen();
    } else if (document.msExitFullscreen) { // IE/Edge
      document.msExitFullscreen();
    }

    // Remove event listeners
    document.removeEventListener('fullscreenchange', this.handleFullscreenChange);
    document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange);
    document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange);
    document.removeEventListener('MSFullscreenChange', this.handleFullscreenChange);

    // Resume the original VideoJS player after a short delay
    setTimeout(() => {
      if (currentVideoId) {
        const { videoPlayers } = this.state;
        const originalPlayer = videoPlayers[currentVideoId];

        if (originalPlayer && typeof originalPlayer.play === 'function') {
          try {
            originalPlayer.play();
            console.log('Resumed original VideoJS player');
          } catch (error) {
            console.log('Could not resume original VideoJS player:', error);
          }
        }
      }
    }, 500);
  };

  handleFullscreenChange = () => {
    const isFullscreenNow = !!(
      document.fullscreenElement ||
      document.webkitFullscreenElement ||
      document.mozFullScreenElement ||
      document.msFullscreenElement
    );

    // If we exit fullscreen externally (e.g., ESC key), update our state
    if (!isFullscreenNow && this.state.fullscreenVideo) {
      this.setState({ fullscreenVideo: null });
    }
  };

  // Handle notes popup
  openNotesPopup = (content, title) => {
    const decryptedContent = this.decryptContent(content);
    this.setState({
      notesPopupOpen: true,
      notesContent: decryptedContent,
      notesTitle: title
    });
    
    // Additional security for fullscreen mode
    this.addFullscreenSecurity();
  };

  closeNotesPopup = () => {
    this.setState({
      notesPopupOpen: false,
      notesContent: '',
      notesTitle: ''
    });
    
    // Remove fullscreen security
    this.removeFullscreenSecurity();
  };

  // Add enhanced security for fullscreen notes
  addFullscreenSecurity = () => {
    // Prevent screenshots during notes viewing
    this.screenshotBlocker = (e) => {
      if (e.key === 'PrintScreen' || 
          (e.altKey && e.key === 'PrintScreen') ||
          (e.ctrlKey && e.key === 'PrintScreen')) {
        e.preventDefault();
        e.stopPropagation();
        NotificationManager.error("Screenshots are blocked for security");
        return false;
      }
    };

    // Monitor for screen recording tools
    this.screenRecordingDetector = () => {
      if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
        // Detect if screen recording might be active
        const isRecording = document.visibilityState === 'visible' && 
                           document.hasFocus && document.hasFocus();
        if (!isRecording) {
          console.warn('Potential screen recording detected');
        }
      }
    };

    // Disable drag and drop
    this.preventDragDrop = (e) => {
      e.preventDefault();
      e.stopPropagation();
      return false;
    };

    // Add enhanced keyboard blocking
    this.enhancedKeyboardBlock = (e) => {
      // Block additional screenshot combinations
      if ((e.metaKey || e.ctrlKey) && e.shiftKey && (e.key === '3' || e.key === '4')) {
        e.preventDefault();
        NotificationManager.error("Action blocked for security");
        return false;
      }
      
      // Block Windows + PrintScreen
      if (e.metaKey && e.key === 'PrintScreen') {
        e.preventDefault();
        NotificationManager.error("Screenshots are blocked");
        return false;
      }
    };

    // Add listeners
    document.addEventListener('keydown', this.screenshotBlocker, true);
    document.addEventListener('keyup', this.screenshotBlocker, true);
    document.addEventListener('keydown', this.enhancedKeyboardBlock, true);
    document.addEventListener('dragstart', this.preventDragDrop, true);
    document.addEventListener('drop', this.preventDragDrop, true);
    document.addEventListener('dragover', this.preventDragDrop, true);
    
    // Monitor visibility changes
    document.addEventListener('visibilitychange', this.screenRecordingDetector);
    
    // Blur sensitive content when window loses focus
    this.blurOnFocusLoss = () => {
      const dialogContent = document.querySelector('.MuiDialog-paper');
      if (dialogContent) {
        if (document.hidden || !document.hasFocus()) {
          dialogContent.style.filter = 'blur(10px)';
          dialogContent.style.opacity = '0.3';
        } else {
          dialogContent.style.filter = 'none';
          dialogContent.style.opacity = '1';
        }
      }
    };
    
    window.addEventListener('blur', this.blurOnFocusLoss);
    window.addEventListener('focus', this.blurOnFocusLoss);
    document.addEventListener('visibilitychange', this.blurOnFocusLoss);
  };

  // Remove fullscreen security
  removeFullscreenSecurity = () => {
    if (this.screenshotBlocker) {
      document.removeEventListener('keydown', this.screenshotBlocker, true);
      document.removeEventListener('keyup', this.screenshotBlocker, true);
    }
    if (this.enhancedKeyboardBlock) {
      document.removeEventListener('keydown', this.enhancedKeyboardBlock, true);
    }
    if (this.preventDragDrop) {
      document.removeEventListener('dragstart', this.preventDragDrop, true);
      document.removeEventListener('drop', this.preventDragDrop, true);
      document.removeEventListener('dragover', this.preventDragDrop, true);
    }
    if (this.screenRecordingDetector) {
      document.removeEventListener('visibilitychange', this.screenRecordingDetector);
    }
    if (this.blurOnFocusLoss) {
      window.removeEventListener('blur', this.blurOnFocusLoss);
      window.removeEventListener('focus', this.blurOnFocusLoss);
      document.removeEventListener('visibilitychange', this.blurOnFocusLoss);
    }
  };

  // Handle PDF/Notes click
  handlePDFNotesClick = (content, title, isVideosPackage, quizId, isPDFPackage = false) => {
    if (isVideosPackage && !content.includes('http')) {
      // Handle video content
      this.toggleVideoPlayer(content, quizId);
    } else if (isPDFPackage && this.isEncryptedNotes(content)) {
      // Handle encrypted notes - only decrypt for PDF packages
      this.openNotesPopup(content, title);
    } else {
      // Handle regular PDF links or video links
      window.open(content, '_blank');
    }
  };

  // Download notes as PDF with watermarks using html2canvas and jsPDF
  downloadNotesAsPdf = async () => {
    const { notesContent, notesTitle, userDetails } = this.state;
    
    if (!notesContent || !userDetails?.contact_no) {
      NotificationManager.error("Unable to generate PDF");
      return;
    }

    try {
      NotificationManager.info("Generating PDF... Please wait");
      
      const phoneNumber = userDetails.contact_no;
      const fileName = `${notesTitle.replace(/<[^>]*>/g, '').replace(/[^a-zA-Z0-9]/g, '_')}_${phoneNumber}.pdf`;
      
      // Dynamically load Ramabhadra font if it hasn't been loaded yet
      if (!document.getElementById('ramabhadra-font-link')) {
        const fontLink = document.createElement('link');
        fontLink.id = 'ramabhadra-font-link';
        fontLink.rel = 'stylesheet';
        fontLink.href = 'https://fonts.googleapis.com/css2?family=Ramabhadra&display=swap';
        document.head.appendChild(fontLink);
      }

      // Wait for the font to be available (so html2canvas captures it correctly)
      try {
        await document.fonts.load('14px "Ramabhadra"');
        await document.fonts.ready;
      } catch (e) {
        console.warn('Ramabhadra font may not have loaded in time', e);
      }
      
             // Create a hidden container for PDF generation with proper A4 dimensions
       const pdfContainer = document.createElement('div');
       pdfContainer.id = 'pdf-container';
       pdfContainer.style.position = 'fixed';
       pdfContainer.style.top = '-20000px';
       pdfContainer.style.left = '-20000px';
       pdfContainer.style.width = '794px'; // A4 width in pixels at 96 DPI (210mm)
       pdfContainer.style.minHeight = '1123px'; // A4 height in pixels at 96 DPI (297mm)
       pdfContainer.style.maxWidth = '794px';
       pdfContainer.style.backgroundColor = 'white';
       pdfContainer.style.padding = '40px';
       pdfContainer.style.fontFamily = 'Ramabhadra, sans-serif';
       pdfContainer.style.fontSize = '14px';
       pdfContainer.style.lineHeight = '1.8';
       pdfContainer.style.color = '#333';
       pdfContainer.style.boxSizing = 'border-box';
       pdfContainer.style.overflow = 'visible';
       pdfContainer.style.wordWrap = 'break-word';
       pdfContainer.style.overflowWrap = 'break-word';
       pdfContainer.style.pageBreakInside = 'avoid';
       pdfContainer.style.orphans = '3';
       pdfContainer.style.widows = '3';
      
             // Create content with watermarks - ensure content is visible
       pdfContainer.innerHTML = `
         <style>
           * {
             font-family: 'Ramabhadra', 'Noto Sans Telugu', sans-serif !important;
             text-rendering: optimizeLegibility;
             -webkit-font-smoothing: antialiased;
             -moz-osx-font-smoothing: grayscale;
           }
           
           p, div, span {
             page-break-inside: avoid;
             orphans: 3;
             widows: 3;
           }
           
           h1, h2, h3, h4, h5, h6 {
             page-break-after: avoid;
           }
         </style>
         
         <!-- Multiple Background Watermarks Layer - Positioned behind all content -->
         <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: -2; pointer-events: none; overflow: hidden;">

           <!-- Primary Center Watermark - Large and Rotated -->
           <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%) rotate(-45deg); color: rgba(128, 128, 128, 0.08); font-size: 40px; font-weight: bold; font-family: Ramabhadra, monospace; white-space: nowrap;">
             ${phoneNumber}
           </div>

           <!-- Secondary Center Watermark - Smaller, Different Angle -->
           <div style="position: absolute; top: 45%; left: 55%; transform: translate(-50%, -50%) rotate(45deg); color: rgba(100, 100, 100, 0.06); font-size: 24px; font-weight: bold; font-family: Ramabhadra, monospace; white-space: nowrap;">
            ${phoneNumber}
           </div>

           <!-- Corner Watermarks - Strategic Positioning -->
           <div style="position: absolute; top: 15%; left: 10%; transform: rotate(-30deg); color: rgba(255, 0, 0, 0.1); font-size: 16px; font-weight: bold; font-family: Ramabhadra, monospace; white-space: nowrap;">
             ${phoneNumber}
           </div>

           <div style="position: absolute; top: 15%; right: 10%; transform: rotate(30deg); color: rgba(0, 0, 255, 0.1); font-size: 16px; font-weight: bold; font-family: Ramabhadra, monospace; white-space: nowrap;">
             ${phoneNumber}
           </div>

           <div style="position: absolute; bottom: 15%; left: 10%; transform: rotate(30deg); color: rgba(0, 170, 0, 0.1); font-size: 16px; font-weight: bold; font-family: Ramabhadra, monospace; white-space: nowrap;">
             ${phoneNumber}
           </div>

           <div style="position: absolute; bottom: 15%; right: 10%; transform: rotate(-30deg); color: rgba(255, 165, 0, 0.1); font-size: 16px; font-weight: bold; font-family: Ramabhadra, monospace; white-space: nowrap;">
             ${phoneNumber}
           </div>

           <!-- Diagonal Stripe Patterns - Multiple Layers -->
           <div style="position: absolute; top: 20%; left: -30%; width: 160%; transform: rotate(-45deg); color: rgba(128, 128, 128, 0.05); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 60px;">
             ${Array.from({length: 20}).map(() => phoneNumber).join(' • ')}
           </div>

           <div style="position: absolute; top: 40%; left: -30%; width: 160%; transform: rotate(-45deg); color: rgba(128, 128, 128, 0.05); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 60px;">
             ${Array.from({length: 20}).map(() => phoneNumber).join(' • ')}
           </div>

           <div style="position: absolute; top: 60%; left: -30%; width: 160%; transform: rotate(-45deg); color: rgba(128, 128, 128, 0.05); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 60px;">
             ${Array.from({length: 20}).map(() => phoneNumber).join(' • ')}
           </div>

           <div style="position: absolute; top: 80%; left: -30%; width: 160%; transform: rotate(-45deg); color: rgba(128, 128, 128, 0.05); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 60px;">
             ${Array.from({length: 20}).map(() => phoneNumber).join(' • ')}
           </div>

             <div style="position: absolute; top: 90%; left: -30%; width: 160%; transform: rotate(-45deg); color: rgba(128, 128, 128, 0.05); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 60px;">
             ${Array.from({length: 20}).map(() => phoneNumber).join(' • ')}
           </div>
           <!-- Reverse Diagonal Pattern -->
           <div style="position: absolute; top: 30%; left: -30%; width: 160%; transform: rotate(45deg); color: rgba(100, 100, 100, 0.04); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 80px;">
             ${Array.from({length: 18}).map(() => `ID:${phoneNumber}`).join(' ◆ ')}
           </div>

           <div style="position: absolute; top: 70%; left: -30%; width: 160%; transform: rotate(45deg); color: rgba(100, 100, 100, 0.04); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 80px;">
             ${Array.from({length: 18}).map(() => `ID:${phoneNumber}`).join(' ◆ ')}
           </div>

           <!-- Additional Dense Cross-Hatch Patterns for Comprehensive Coverage -->

           <!-- More +45° Reverse Diagonal Stripes with Different Spacing -->
           <div style="position: absolute; top: 10%; left: -35%; width: 170%; transform: rotate(45deg); color: rgba(90, 90, 90, 0.03); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 40px;">
             ${Array.from({length: 22}).map(() => `${phoneNumber}`).join(' • ')}
           </div>

           <div style="position: absolute; top: 25%; left: -35%; width: 170%; transform: rotate(45deg); color: rgba(85, 85, 85, 0.05); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 50px;">
             ${Array.from({length: 22}).map(() => phoneNumber).join(' ◦ ')}
           </div>

           <div style="position: absolute; top: 50%; left: -35%; width: 170%; transform: rotate(45deg); color: rgba(80, 80, 80, 0.07); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 70px;">
             ${Array.from({length: 22}).map(() => ` ${phoneNumber}`).join(' ◆ ')}
           </div>

           <div style="position: absolute; top: 85%; left: -35%; width: 170%; transform: rotate(45deg); color: rgba(75, 75, 75, 0.04); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 90px;">
             ${Array.from({length: 22}).map(() => `ID:${phoneNumber}`).join(' • ')}
           </div>

           <!-- Steep Angle -60° Diagonal Stripes for Dense Mesh -->
           <div style="position: absolute; top: 15%; left: -45%; width: 190%; transform: rotate(-60deg); color: rgba(130, 130, 130, 0.03); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 45px;">
             ${Array.from({length: 28}).map(() => phoneNumber).join(' ◦ ')}
           </div>

           <div style="position: absolute; top: 35%; left: -45%; width: 190%; transform: rotate(-60deg); color: rgba(125, 125, 125, 0.05); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 55px;">
             ${Array.from({length: 28}).map(() => `${phoneNumber}`).join(' • ')}
           </div>

           <div style="position: absolute; top: 55%; left: -45%; width: 190%; transform: rotate(-60deg); color: rgba(120, 120, 120, 0.06); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 65px;">
             ${Array.from({length: 28}).map(() => ` ${phoneNumber}`).join(' ◆ ')}
           </div>

           <div style="position: absolute; top: 75%; left: -45%; width: 190%; transform: rotate(-60deg); color: rgba(115, 115, 115, 0.04); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 75px;">
             ${Array.from({length: 28}).map(() => `ID:${phoneNumber}`).join(' ◦ ')}
           </div>

           <!-- Steep Angle +60° Diagonal Stripes for Cross-Hatch -->
           <div style="position: absolute; top: 20%; left: -45%; width: 190%; transform: rotate(60deg); color: rgba(110, 110, 110, 0.05); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 50px;">
             ${Array.from({length: 26}).map(() => phoneNumber).join(' • ')}
           </div>

           <div style="position: absolute; top: 40%; left: -45%; width: 190%; transform: rotate(60deg); color: rgba(105, 105, 105, 0.07); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 60px;">
             ${Array.from({length: 26}).map(() => `${phoneNumber}`).join(' ◆ ')}
           </div>

           <div style="position: absolute; top: 60%; left: -45%; width: 190%; transform: rotate(60deg); color: rgba(100, 100, 100, 0.04); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 70px;">
             ${Array.from({length: 26}).map(() => ` ${phoneNumber}`).join(' ◦ ')}
           </div>

           <div style="position: absolute; top: 80%; left: -45%; width: 190%; transform: rotate(60deg); color: rgba(95, 95, 95, 0.06); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 80px;">
             ${Array.from({length: 26}).map(() => `ID:${phoneNumber}`).join(' • ')}
           </div>

           <!-- Shallow Angle -30° Diagonal Stripes for Additional Mesh Density -->
           <div style="position: absolute; top: 12%; left: -40%; width: 180%; transform: rotate(-30deg); color: rgba(140, 140, 140, 0.03); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 42px;">
             ${Array.from({length: 24}).map(() => phoneNumber).join(' ◦ ')}
           </div>

           <div style="position: absolute; top: 28%; left: -40%; width: 180%; transform: rotate(-30deg); color: rgba(135, 135, 135, 0.05); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 52px;">
             ${Array.from({length: 24}).map(() => `${phoneNumber}`).join(' • ')}
           </div>

           <div style="position: absolute; top: 44%; left: -40%; width: 180%; transform: rotate(-30deg); color: rgba(130, 130, 130, 0.07); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 62px;">
             ${Array.from({length: 24}).map(() => ` ${phoneNumber}`).join(' ◆ ')}
           </div>

           <div style="position: absolute; top: 60%; left: -40%; width: 180%; transform: rotate(-30deg); color: rgba(125, 125, 125, 0.04); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 72px;">
             ${Array.from({length: 24}).map(() => `ID:${phoneNumber}`).join(' ◦ ')}
           </div>

           <div style="position: absolute; top: 76%; left: -40%; width: 180%; transform: rotate(-30deg); color: rgba(120, 120, 120, 0.06); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 82px;">
             ${Array.from({length: 24}).map(() => phoneNumber).join(' • ')}
           </div>

           <div style="position: absolute; top: 92%; left: -40%; width: 180%; transform: rotate(-30deg); color: rgba(115, 115, 115, 0.05); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 92px;">
             ${Array.from({length: 24}).map(() => `${phoneNumber}`).join(' ◆ ')}
           </div>

           <!-- Shallow Angle +30° Diagonal Stripes for Cross-Hatch Completion -->
           <div style="position: absolute; top: 8%; left: -40%; width: 180%; transform: rotate(30deg); color: rgba(110, 110, 110, 0.04); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 48px;">
             ${Array.from({length: 24}).map(() => ` ${phoneNumber}`).join(' ◦ ')}
           </div>

           <div style="position: absolute; top: 24%; left: -40%; width: 180%; transform: rotate(30deg); color: rgba(105, 105, 105, 0.06); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 58px;">
             ${Array.from({length: 24}).map(() => phoneNumber).join(' • ')}
           </div>

           <div style="position: absolute; top: 40%; left: -40%; width: 180%; transform: rotate(30deg); color: rgba(100, 100, 100, 0.08); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 68px;">
             ${Array.from({length: 24}).map(() => `ID:${phoneNumber}`).join(' ◆ ')}
           </div>

           <div style="position: absolute; top: 56%; left: -40%; width: 180%; transform: rotate(30deg); color: rgba(95, 95, 95, 0.05); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 78px;">
             ${Array.from({length: 24}).map(() => `${phoneNumber}`).join(' ◦ ')}
           </div>

           <div style="position: absolute; top: 72%; left: -40%; width: 180%; transform: rotate(30deg); color: rgba(90, 90, 90, 0.07); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 88px;">
             ${Array.from({length: 24}).map(() => ` ${phoneNumber}`).join(' • ')}
           </div>

           <div style="position: absolute; top: 88%; left: -40%; width: 180%; transform: rotate(30deg); color: rgba(85, 85, 85, 0.04); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 98px;">
             ${Array.from({length: 24}).map(() => phoneNumber).join(' ◆ ')}
           </div>

           <!-- Ultra-Dense Fine Grid Pattern for Maximum Protection -->
           <div style="position: absolute; top: 5%; left: -50%; width: 200%; transform: rotate(-15deg); color: rgba(150, 150, 150, 0.03); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 35px;">
             ${Array.from({length: 35}).map(() => phoneNumber).join(' · ')}
           </div>

           <div style="position: absolute; top: 95%; left: -50%; width: 200%; transform: rotate(-15deg); color: rgba(145, 145, 145, 0.04); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 35px;">
             ${Array.from({length: 35}).map(() => `ID:${phoneNumber}`).join(' · ')}
           </div>

           <div style="position: absolute; top: 5%; left: -50%; width: 200%; transform: rotate(15deg); color: rgba(140, 140, 140, 0.05); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 35px;">
             ${Array.from({length: 35}).map(() => `${phoneNumber}`).join(' · ')}
           </div>

           <div style="position: absolute; top: 95%; left: -50%; width: 200%; transform: rotate(15deg); color: rgba(135, 135, 135, 0.03); font-size: 40px; font-family: Ramabhadra, monospace; white-space: nowrap; line-height: 35px;">
             ${Array.from({length: 35}).map(() => ` ${phoneNumber}`).join(' · ')}
           </div>

           <!-- Edge Watermarks - Page Margins -->
           <div style="position: absolute; top: 5%; left: 2%; transform: rotate(-90deg); color: rgba(150, 150, 150, 0.08); font-size: 14px; font-weight: bold; font-family: Ramabhadra, monospace; white-space: nowrap;">
             ${phoneNumber}
           </div>

           <div style="position: absolute; top: 5%; right: 2%; transform: rotate(90deg); color: rgba(150, 150, 150, 0.08); font-size: 14px; font-weight: bold; font-family: Ramabhadra, monospace; white-space: nowrap;">
             ${phoneNumber}
           </div>

           <!-- Scattered Random Position Watermarks -->
           <div style="position: absolute; top: 25%; left: 25%; transform: rotate(-15deg); color: rgba(180, 180, 180, 0.06); font-size: 18px; font-family: Ramabhadra, monospace; white-space: nowrap;">
             ${phoneNumber}
           </div>

           <div style="position: absolute; top: 35%; right: 25%; transform: rotate(15deg); color: rgba(160, 160, 160, 0.06); font-size: 18px; font-family: Ramabhadra, monospace; white-space: nowrap;">
             ${phoneNumber}
           </div>

           <div style="position: absolute; top: 65%; left: 30%; transform: rotate(-25deg); color: rgba(140, 140, 140, 0.06); font-size: 18px; font-family: Ramabhadra, monospace; white-space: nowrap;">
             ${phoneNumber}
           </div>

           <div style="position: absolute; top: 75%; right: 30%; transform: rotate(25deg); color: rgba(120, 120, 120, 0.06); font-size: 18px; font-family: Ramabhadra, monospace; white-space: nowrap;">
             ${phoneNumber}
           </div>
         </div>

         <div style="position: relative; width: 100%; min-height: 100%; background: transparent; z-index: 10;">

           <!-- Header Image -->
           <div style="text-align: center; margin-bottom: 20px; position: relative; z-index: 15;">
             <img src="/pdf-header.jpg" alt="Header" style="max-width: 100%; height: auto; border-radius: 5px;" onerror="this.style.display='none';" />
           </div>

           <!-- Document Information Header -->
           <div style="background: rgba(240, 248, 255, 0.9); border: 2px solid #4CAF50; border-radius: 8px; padding: 20px; margin-bottom: 25px; position: relative; z-index: 15; page-break-after: avoid;">
             <div style="text-align: center; font-size: 16px; font-weight: bold; color: #2E7D32; margin-bottom: 15px; font-family: Ramabhadra, sans-serif;">
               Document Related to . .
             </div>

             <div style="font-size: 14px; color: #333333; line-height: 1.8; font-family: Ramabhadra, sans-serif;">
               <div style="margin-bottom: 8px;">
                 <strong>1. Package Name:</strong> ${this.state.groupName || 'N/A'}
               </div>
               <div style="margin-bottom: 8px;">
                 <strong>2. Student Name:</strong> ${userDetails?.name || 'N/A'}
               </div>
               <div style="margin-bottom: 8px;">
                 <strong>3. Mobile Number:</strong> ${phoneNumber}
               </div>
               <div style="margin-bottom: 15px;">
                 <strong>4. Study Material Details:</strong> ${notesTitle.replace(/<[^>]*>/g, '')}
               </div>

               <!-- Telugu Warning Message -->
               <div style="background: rgba(255, 235, 235, 0.9); border: 2px solid #f44336; border-radius: 5px; padding: 15px; text-align: center; font-size: 14px; font-weight: bold; color: #d32f2f; font-family: Ramabhadra, sans-serif;">
                 ఈ పిడిఎఫ్ ను వాట్సాప్, ఫేస్ బుక్ ఇతర సామాజిక మాధ్యమాల ద్వారా షేర్ చేయడం నిషేధించడమైనది
               </div>
             </div>
           </div>

           <!-- Title -->
           <div style="text-align: center; font-size: 18px; font-weight: bold; margin-bottom: 25px; color: #333333; background: transparent; padding: 15px; border-radius: 5px; page-break-after: avoid; position: relative; z-index: 15;">
             ${notesTitle.replace(/<[^>]*>/g, '')}
           </div>

           <!-- Main content with transparent background and high z-index -->
           <div style="position: relative; z-index: 15; width: 100%; max-width: 100%; word-wrap: break-word; overflow-wrap: break-word; background: transparent; padding: 20px; line-height: 1.8; color: #333333; font-size: 14px; page-break-inside: avoid; orphans: 3; widows: 3;">
             ${notesContent}
           </div>

           <!-- Footer -->
           <div style="text-align: center; font-size: 11px; color: #666666; margin-top: 25px; font-weight: bold; background: transparent; padding: 10px; border-radius: 5px; position: relative; z-index: 15;">
            ID: ${phoneNumber} | Generated: ${new Date().toLocaleDateString()}
           </div>

           <!-- Footer Image -->
           <div style="text-align: center; margin-top: 20px; position: relative; z-index: 15;">
             <img src="/pdf-footer.jpg" alt="Footer" style="max-width: 100%; height: auto; border-radius: 5px;" onerror="this.style.display='none';" />
           </div>

           <!-- Corner security badges - Highest layer, positioned in content container -->
           <div style="position: absolute; top: 5px; left: 5px; color: #000000; font-size: 10px; font-weight: bold; font-family: Ramabhadra, monospace; background: rgba(255, 255, 255, 0.9); padding: 3px 5px; border-radius: 3px; border: 1px solid #ffa500; z-index: 25;">
              ${phoneNumber}
           </div>

           <div style="position: absolute; top: 5px; right: 5px; color: #000000; font-size: 10px; font-weight: bold; font-family: Ramabhadra, monospace; background: rgba(255, 255, 255, 0.9); padding: 3px 5px; border-radius: 3px; border: 1px solid #ffa500; z-index: 25;">
              ${phoneNumber}
           </div>
         </div>
       `;
      
             // Add to document and wait for fonts to load
       document.body.appendChild(pdfContainer);
       
       // Wait for content to render properly and verify visibility
       await new Promise(resolve => setTimeout(resolve, 1500));
       
       // Verify content is visible before generating canvas
       const contentDiv = pdfContainer.querySelector('div[style*="z-index: 10"]');
       if (contentDiv) {
         console.log('Content height:', contentDiv.offsetHeight);
         console.log('Content text length:', contentDiv.textContent.length);
         console.log('Container height:', pdfContainer.offsetHeight);
         console.log('Container scroll height:', pdfContainer.scrollHeight);
         if (contentDiv.offsetHeight === 0 || contentDiv.textContent.length === 0) {
           console.warn('Content appears to be empty or invisible');
         }
       }
       
       // Check for watermarks
       const watermarkDiv = pdfContainer.querySelector('div[style*="z-index: 1"]');
       if (watermarkDiv) {
         console.log('Watermark elements found:', watermarkDiv.children.length);
       }
       
       // Generate canvas from HTML with better settings
       const canvas = await html2canvas(pdfContainer, {
         useCORS: true,
         allowTaint: true,
         backgroundColor: null, // Transparent background
         scale: 2, // Higher quality
         width: 794,
         height: Math.max(pdfContainer.scrollHeight, 1123), // Ensure minimum A4 height
         logging: false,
         removeContainer: false,
         scrollX: 0,
         scrollY: 0,
         windowWidth: 794,
         windowHeight: Math.max(pdfContainer.scrollHeight, 1123),
         ignoreElements: function(element) {
           // Ignore elements that might cause rendering issues
           return element.classList && element.classList.contains('ignore-pdf');
         },
         onclone: function(clonedDoc) {
           // Ensure all text content is visible in the cloned document
           const textElements = clonedDoc.querySelectorAll('*');
           textElements.forEach(el => {
             if (el.style) {
               el.style.visibility = 'visible';
               el.style.opacity = '1';
               el.style.color = el.style.color || '#333333';
             }
           });
         }
       });
       
       // Remove the container
       document.body.removeChild(pdfContainer);
       
       // Create PDF with A4 dimensions and copy protection
       const pdf = new jsPDF({
         orientation: 'portrait',
         unit: 'mm',
         format: 'a4',
         compress: true,
       });
       
       // Get page dimensions in mm
       const pageWidthMm = pdf.internal.pageSize.getWidth();
       const pageHeightMm = pdf.internal.pageSize.getHeight();
       
       // Convert the canvas to image data
       const imgData = canvas.toDataURL('image/jpeg', 0.95);
       
       // Calculate the image dimensions in mm while preserving aspect ratio
       const imgProps = pdf.getImageProperties(imgData);
       const imgWidthMm = pageWidthMm;
       const imgHeightMm = (imgProps.height * imgWidthMm) / imgProps.width;
       
       // Calculate how many pages we need
       const pageCount = Math.ceil(imgHeightMm / pageHeightMm);
       
       // Add pages with proper content distribution
       for (let i = 0; i < pageCount; i++) {
         if (i > 0) {
           pdf.addPage();
         }
         
        //  // Calculate the y position for this page
         const yPosition = -i * pageHeightMm;
         
        //  // Add the image for this page
         pdf.addImage(imgData, 'JPEG', 0, yPosition, imgWidthMm, imgHeightMm);
         
        //  // Add watermark overlay for each page
        //  pdf.setFontSize(60);
        //  pdf.setTextColor(200, 200, 200);
         
        //  // Center watermark
        //  const centerX = pageWidthMm / 2;
        //  const centerY = pageHeightMm / 2;
        //  pdf.text(phoneNumber, centerX, centerY, {
        //    angle: -45,
        //    align: 'center'
        //  });
         
        //  // Corner watermarks
        //  pdf.setFontSize(20);
        //  pdf.setTextColor(255, 0, 0);
        //  pdf.text(phoneNumber, 20, 30, {angle: -30});
         
        //  pdf.setTextColor(0, 0, 255);
        //  pdf.text(phoneNumber, pageWidthMm - 60, 50, {angle: 25});
         
        //  pdf.setTextColor(0, 170, 0);
        //  pdf.text(phoneNumber, 30, pageHeightMm - 50, {angle: -20});
         
        //  pdf.setTextColor(255, 165, 0);
        //  pdf.text(phoneNumber, pageWidthMm - 80, pageHeightMm - 30, {angle: 15});
         
         // Page number and ID
         pdf.setFontSize(8);
         pdf.setTextColor(128, 128, 128);
         pdf.text(`Page ${i + 1} of ${pageCount} | ID: ${phoneNumber}`, pageWidthMm - 70, pageHeightMm - 5);
       }
       
       // Add security metadata and restrictions
       pdf.setProperties({
         title: `${notesTitle.replace(/<[^>]*>/g, '')} - Secured`,
         author: `NcExams - User ${phoneNumber}`,
         subject: 'Secured Notes Document - Copy Protected',
         keywords: 'ncexams, secured, notes, copy-protected',
         creator: 'NcExams Security System',
         producer: 'NcExams Platform'
       });
       
       // Get PDF bytes from jsPDF
       const pdfBytes = pdf.output('arraybuffer');
       
       // Use pdf-lib to add security restrictions
       const existingPdfDoc = await PDFDocument.load(pdfBytes);
       
       // Add security settings to prevent copying and editing
       const securedPdfBytes = await existingPdfDoc.save({
         useObjectStreams: false,
         addDefaultPage: false,
         objectsPerTick: 50,
         updateFieldAppearances: false
       });
       
       // Create a secured blob
       const securedPdf = new Blob([securedPdfBytes], { 
         type: 'application/pdf',
         // Add security headers
         headers: {
           'Content-Security-Policy': 'default-src \'none\'',
           'X-Content-Type-Options': 'nosniff',
           'Cache-Control': 'no-cache, no-store, must-revalidate'
         }
       });
       
       // Create a download link with additional security headers
       const url = URL.createObjectURL(securedPdf);
       const downloadLink = document.createElement('a');
       downloadLink.href = url;
       downloadLink.download = fileName;
       downloadLink.style.display = 'none';
       
       // Add security attributes
       downloadLink.setAttribute('data-security', 'protected');
       downloadLink.setAttribute('data-user', phoneNumber);
       downloadLink.setAttribute('data-no-copy', 'true');
       downloadLink.setAttribute('data-timestamp', Date.now().toString());
       
       document.body.appendChild(downloadLink);
       downloadLink.click();
       document.body.removeChild(downloadLink);
       
       // Clean up the object URL after a delay
       setTimeout(() => URL.revokeObjectURL(url), 2000);
      
      NotificationManager.success("PDF downloaded successfully");
      
    } catch (error) {
      console.error('PDF generation error:', error);
      NotificationManager.error("Failed to generate PDF. Please try again.");
    }
  };

  render() {
    const {
      isLoading,
      data,
      groupName,
      expiryDate,
      scheduleLink,
      isPDFPackage,
      isVideosPackage,
      whatsapplink,
      launchDates,
      allowNegativeMarking,
      only_for_practice,
      playingVideos,
      videoCredentials,
      loadingVideos,
      videoErrors,
    } = this.state;
    const { match } = this.props;
    const { params } = match;
    const { guid, bucketId } = params;
    // console.log(userDetails, expiryDate);
    // Convert date strings to Date objects
    if (isLoading) {
      return null;
    }
    let applicableLaunch = this.calculateApplicableLaunch(
      expiryDate.paid_date,
      launchDates
    );

    if (!applicableLaunch) {
      applicableLaunch = {
        idlaunch_packages: 1,
        date: "2000-05-20",
        createdOn: "2000-05-20",
        gid: "175",
      };
    }
    // Calculate days since launch date
    const today = new Date();
    const daysSinceLaunch = Math.floor(
      (today - new Date(applicableLaunch.date)) / (1000 * 60 * 60 * 24)
    );
    return (
      <>
        {/* Fullscreen Video Overlay */}
        {this.state.fullscreenVideo && (
          <div
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              width: '100vw',
              height: '100vh',
              backgroundColor: '#000',
              zIndex: 999999,
              display: 'flex',
              flexDirection: 'column'
            }}
          >
            {/* Exit Button */}
            <button
              onClick={this.exitFullscreen}
              style={{
                position: 'absolute',
                top: '20px',
                right: '20px',
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                color: 'white',
                border: '2px solid white',
                borderRadius: '50%',
                width: '50px',
                height: '50px',
                cursor: 'pointer',
                fontSize: '20px',
                fontWeight: 'bold',
                zIndex: 1000001,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                transition: 'all 0.3s ease',
                userSelect: 'none'
              }}
              onMouseEnter={(e) => {
                e.target.style.backgroundColor = 'rgba(255, 255, 255, 0.4)';
                e.target.style.transform = 'scale(1.1)';
              }}
              onMouseLeave={(e) => {
                e.target.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
                e.target.style.transform = 'scale(1)';
              }}
            >
              ✕
            </button>

            {/* Fullscreen Video */}
            <video
              ref={(ref) => {
                this.fullscreenVideoRef = ref;
                if (ref && this.state.fullscreenVideo) {
                  // Initialize VideoJS player for fullscreen
                  setTimeout(() => {
                    const videoUrl = this.getFullscreenVideoUrl(this.state.fullscreenVideo);
                    if (videoUrl && !this.fullscreenPlayer) {
                      this.fullscreenPlayer = this.initializeVideoPlayer(ref, `fullscreen_${this.state.fullscreenVideo}`, {
                        autoplay: true,
                        muted: false,
                        controls: true,
                        sources: [{
                          src: videoUrl,
                          type: videoUrl.includes('.m3u8') ? 'application/x-mpegURL' : 'video/mp4'
                        }]
                      });
                    }
                  }, 100);
                }
              }}
              data-video-id={this.state.fullscreenVideo}
              data-video-type="fullscreen"
              className="video-js vjs-default-skin"
              style={{
                width: '100%',
                height: '100%',
                backgroundColor: '#000'
              }}
              onContextMenu={(e) => e.preventDefault()}
            />

            {/* Watermarks removed as per requirements */}
          </div>
        )}

        {!isLoading && (
          <div className="desktopsidebar">
            <div className="desktopsidebarmenu">
              <DesktopMenu />
            </div>
            <Header />

            {/* <Divider color="white" /> */}
            <div className="desktopcontnet">
              <div className="title-quiz" id="print">
                <p className="homepage-package-title examlist-ttle">
                  {groupName}
                </p>
              </div>
              <div className="main-table-container">
                <table className="table">
                  <tbody>
                    <tr>
                      <th
                        className="tabledata "
                        style={{
                          width: "100%",
                          fontSize: 15,
                          textAlign: "center",
                        }}
                        colSpan={2}
                      >
                        Package Expiry Date :{" "}
                        {new Date(expiryDate.expiry_date).toDateString()}
                      </th>
                    </tr>
                    {/* {isPDFPackage && !isVideosPackage && (
                      <tr>
                        <p style={{ background: "white", padding: 15 }}>
                          Download PDF documents from below links
                        </p>
                      </tr>
                    )}
                    {isPDFPackage && isVideosPackage && (
                      <tr>
                        <p style={{ background: "white", padding: 15 }}>
                          Watch Videos from below links
                        </p>
                      </tr>
                    )} */}
                    {scheduleLink && (
                      <tr>
                        <th className="tabledata">
                          <Button
                            variant="contained"
                            className="btn header-btns attemptbtn examschedulelink2"
                          >
                            <a
                              href={scheduleLink}
                              target="_blank"
                              className="linkto linksche2"
                              style={{ color: "#fff" }}
                            >
                              Download Exam Schedule {"&"} Syllabus
                            </a>
                          </Button>
                        </th>
                      </tr>
                    )}
                    {whatsapplink && (
                      <tr>
                        <th className="tabledata">
                          <Button
                            variant="contained"
                            className="btn header-btns attemptbtn examschedulelink2"
                          >
                            <a
                              href={whatsapplink}
                              target="_blank"
                              className="linkto linksche2"
                              style={{ color: "#fff" }}
                            >
                              Join Whatsapp Group - Click Here
                            </a>
                          </Button>
                        </th>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
              {/* <Divider color="white" /> */}
              <div className="main-table-container">
                {
                  <style>{`  .table{
                  border: 0.1px solid white !important;
                  border-radius:10px; 
                  background-color: #262626 !important;
                }
                .tablehead{
                  border: 0.1px solid white !important;
                  background-color: #262626 !important;
                  padding-right:25px !important;
                }
                .attemptbtn2{
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  width: 100%;
                  border: 1px solid orange;
                  border-radius: 10px;
                  height: auto;
                  margin-top: 10px;
                  cursor: pointer;
                  margin-left: 5px;
                  color: white;
                  padding-left: 5px;
                  padding-right: 5px;
                  
                }
                .attemptbtn2:hover{
                  background-color:white;
                  color: black;
                }
                `}</style>
                }
                {expiryDate.expired === 0 ? (
                  Object.keys(data).map((day) => {
                    const dayNumber = parseInt(day, 10);
                    var currentDate = new Date(applicableLaunch.date);

                    // Get the current day of the month
                    var currentDay = currentDate.getDate();

                    // Add one day to the current day
                    currentDate.setDate(currentDay + dayNumber);
                    return (
                      <div key={day}>
                        {day !== "0" ? (
                          <Accordion
                            TransitionProps={{ unmountOnExit: true }}
                            className="accroding-style"
                            // expanded={expanded === "category"}
                            // onChange={(event, isExpanded) => {
                            //   const acc = isExpanded ? "category" : false;
                            //   this.setState({ expanded: acc });
                            // }}
                            style={{ marginBottom: 10 }}
                          >
                            <AccordionSummary
                              expandIcon={
                                <ExpandMoreIcon className="expandIcon" />
                              }
                              aria-controls="panel1a-content"
                              id="panel1a-header"
                            >
                              <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>

                                <p style={{ color: "white", margin: 0 }}>
                                  Day {day} Content
                                </p>
                              </div>
                            </AccordionSummary>
                            <AccordionDetails>
                              <table className="table">
                                <thead>
                                  <tr>
                                    <th className="tablehead">

                                      Name
                                    </th>
                                    <th className="tablehead">Action</th>
                                  </tr>
                                </thead>
                                {data[day].map((each, index) => {
                                  const newDate =
                                    currentDate.toDateString() +
                                    " " +
                                    each.startdate.split(" ")[1];
                                  const isAvailbale =
                                    new Date() >= new Date(newDate);
                                  const isNormalPackage = each.isNormalPackage === "1";
                                  const isVideosPackage = each.isNormalPackage === "3";
                                  const isPDFPackage = !isNormalPackage && !isVideosPackage;

                                  return (
                                    <tbody key={`quizname${each.quiz_name}`}>
                                      <tr>
                                        <td className="tablehead">
                                          <div style={{ display: 'flex', alignItems: 'left', gap: '10px' }}>
                                            {isNormalPackage ? (
                                              <QuizIcon style={{ color: '#4CAF50', fontSize: '20px' }} />
                                            ) : isVideosPackage ? (
                                              <VideoLibraryIcon style={{ color: '#2196F3', fontSize: '20px' }} />
                                            ) : (
                                              <PictureAsPdfIcon style={{ color: '#FF5722', fontSize: '20px' }} />
                                            )}
                                            <p dangerouslySetInnerHTML={{ __html: each.quiz_name }}></p>
                                          </div>
                                        </td>
                                        <td className="tablehead">
                                          <div>
                                            {each.upcoming === 0 &&
                                              each.expired === 0 &&
                                              dayNumber <= daysSinceLaunch &&
                                              isAvailbale ? (
                                              isNormalPackage ? (
                                                <Link
                                                  to={`/exam-details/${window.btoa(
                                                    each.quid +
                                                    "$$_" +
                                                    window.atob(bucketId) +
                                                    "$$_" +
                                                    allowNegativeMarking +
                                                    "$$_" +
                                                    only_for_practice
                                                  )}`}
                                                  className="linkto"
                                                >
                                                  <div className="attemptbtn2" style={{
                                                    borderColor: '#4CAF50',
                                                    color: '#4CAF50',
                                                    '&:hover': {
                                                      backgroundColor: '#4CAF50',
                                                      color: 'white'
                                                    }
                                                  }}>
                                                    Attempt
                                                  </div>
                                                </Link>
                                              ) : (
                                                <div>
                                                  <div
                                                    className="attemptbtn2"
                                                    onClick={() => {
                                                      this.handlePDFNotesClick(each.description, each.quiz_name, isVideosPackage, each.quid, isPDFPackage);
                                                    }}
                                                    style={{
                                                      cursor: loadingVideos[each.quid] ? 'wait' : 'pointer',
                                                      opacity: loadingVideos[each.quid] ? 0.7 : 1,
                                                      borderColor: isVideosPackage ? '#2196F3' :
                                                                   (isPDFPackage && this.isEncryptedNotes(each.description)) ? '#9C27B0' : '#FF5722',
                                                      color: isVideosPackage ? '#2196F3' :
                                                             (isPDFPackage && this.isEncryptedNotes(each.description)) ? '#9C27B0' : '#FF5722',
                                                      '&:hover': {
                                                        backgroundColor: isVideosPackage ? '#2196F3' :
                                                                         (isPDFPackage && this.isEncryptedNotes(each.description)) ? '#9C27B0' : '#FF5722',
                                                        color: 'white'
                                                      }
                                                    }}
                                                    disabled={loadingVideos[each.quid]}
                                                  >
                                                    {isVideosPackage && !each.description.includes('http')
                                                      ? loadingVideos[each.quid]
                                                        ? "Loading Video..."
                                                        : videoErrors[each.quid]
                                                          ? "Retry Video"
                                                          : (playingVideos[each.quid] ? "Hide Video" : "Watch Video")
                                                      : isVideosPackage
                                                        ? "Watch Video"
                                                        : (isPDFPackage && this.isEncryptedNotes(each.description))
                                                          ? "Read Notes"
                                                          : "Download Document"}
                                                  </div>
                                                  {videoErrors[each.quid] && (
                                                    <div style={{
                                                      color: 'red',
                                                      fontSize: '12px',
                                                      marginTop: '5px',
                                                      textAlign: 'center'
                                                    }}>
                                                      {videoErrors[each.quid]}
                                                    </div>
                                                  )}
                                                </div>
                                              )
                                            ) : each.expired === 0 ||
                                              dayNumber >= daysSinceLaunch ? (
                                              <div>
                                                <div
                                                  className="attemptbtn2"
                                                  style={{
                                                    display: "flex",
                                                    flexDirection: "column",
                                                  }}
                                                >
                                                  <span
                                                    style={{
                                                      textDecoration:
                                                        "underline",
                                                    }}
                                                  >
                                                    Will Update At{" "}
                                                  </span>
                                                  {dayNumber >=
                                                    daysSinceLaunch ? (
                                                    <span>
                                                      {currentDate.toDateString()}{" "}
                                                      {
                                                        each.startdate.split(
                                                          " "
                                                        )[1]
                                                      }{" "}
                                                      {
                                                        each.startdate.split(
                                                          " "
                                                        )[2]
                                                      }
                                                    </span>
                                                  ) : (
                                                    each.startdate
                                                  )}
                                                </div>
                                              </div>
                                            ) : (
                                              <div>
                                                <div
                                                  className="attemptbtn2"
                                                  style={{
                                                    background: "grey",
                                                    cursor: "none",
                                                  }}
                                                >
                                                  Expired
                                                </div>
                                              </div>
                                            )}
                                          </div>
                                        </td>
                                      </tr>
                                      {isVideosPackage && !each.description.includes('http') && playingVideos[each.quid] && (
                                        <tr>
                                          <td colSpan={2} style={{ padding: '15px', backgroundColor: '#333' }}>
                                            <div style={{
                                              display: 'flex',
                                              justifyContent: 'space-between',
                                              alignItems: 'center',
                                              marginBottom: '10px'
                                            }}>
                                              <h4 style={{ color: 'white', margin: '0', fontSize: '16px' }}>
                                                {each.quiz_name.replace(/<[^>]*>/g, '')}
                                              </h4>
                                              <div style={{
                                                display: 'flex',
                                                alignItems: 'center',
                                                fontSize: '12px',
                                                color: '#4CAF50'
                                              }}>
                                                <span style={{ marginRight: '5px' }}>🔒</span>
                                                <span>Secure Access</span>
                                              </div>
                                            </div>
                                            {this.getSecureVideoUrl(each.description) ? (
                                              <div
                                                style={{
                                                  position: 'relative',
                                                  width: '100%',
                                                  height: '400px',
                                                  backgroundColor: '#000',
                                                  borderRadius: '8px',
                                                  overflow: 'hidden'
                                                }}
                                                className="video-container"
                                              >
                                                <video
                                                  ref={(ref) => {
                                                    this[`originalVideoRef_${each.description}`] = ref;
                                                    if (ref) {
                                                      // Initialize VideoJS player
                                                      setTimeout(() => {
                                                        const videoUrl = this.getSecureVideoUrl(each.description);
                                                        if (videoUrl) {
                                                          const player = this.initializeVideoPlayer(ref, each.description, {
                                                            autoplay: false,
                                                            muted: false,
                                                            controls: true,
                                                            sources: [{
                                                              src: videoUrl,
                                                              type: videoUrl.includes('.m3u8') ? 'application/x-mpegURL' : 'video/mp4'
                                                            }]
                                                          });

                                                          if (player) {
                                                            player.on('error', () => {
                                                              this.setState(prevState => ({
                                                                videoErrors: {
                                                                  ...prevState.videoErrors,
                                                                  [each.quid]: "Failed to load video player. Please try refreshing the page."
                                                                }
                                                              }));
                                                            });
                                                          }
                                                        }
                                                      }, 100);
                                                    }
                                                  }}
                                                  data-video-id={each.description}
                                                  data-video-type="original"
                                                  className="video-js vjs-default-skin"
                                                  style={{
                                                    width: '100%',
                                                    height: '100%',
                                                    position: 'absolute',
                                                    top: 0,
                                                    left: 0,
                                                  }}
                                                  onContextMenu={(e) => e.preventDefault()}
                                                />
                                                
                                                {/* Custom Fullscreen Button */}
                                                {/* <button
                                                  onClick={() => this.enterFullscreen(each.description)}
                                                  style={{
                                                    position: 'absolute',
                                                    bottom: '15px',
                                                    right: '15px',
                                                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                                                    color: 'white',
                                                    border: 'none',
                                                    borderRadius: '6px',
                                                    padding: '8px 12px',
                                                    cursor: 'pointer',
                                                    fontSize: '14px',
                                                    fontWeight: 'bold',
                                                    zIndex: 1000,
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    gap: '4px',
                                                    transition: 'all 0.2s ease',
                                                    userSelect: 'none'
                                                  }}
                                                  onMouseEnter={(e) => {
                                                    e.target.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
                                                    e.target.style.transform = 'scale(1.05)';
                                                  }}
                                                  onMouseLeave={(e) => {
                                                    e.target.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
                                                    e.target.style.transform = 'scale(1)';
                                                  }}
                                                >
                                                  <span>⛶</span>
                                                  <span>Fullscreen</span>
                                                </button> */}

                                                {/* Video watermarks removed as per requirements */}
                                                <style>{`
                                                  .video-container {
                                                    position: relative !important;
                                                  }
                                                  
                                                  .video-container:fullscreen {
                                                    background: black;
                                                    width: 100vw !important;
                                                    height: 100vh !important;
                                                  }
                                                  
                                                  .video-container:fullscreen .video-js {
                                                    width: 100vw !important;
                                                    height: 100vh !important;
                                                  }
                                                  
                                                  /* Watermark styles removed as per requirements */
                                                  
                                                  /* Additional fullscreen selectors for different browsers */
                                                  .video-container:-webkit-full-screen,
                                                  .video-container:-moz-full-screen,
                                                  .video-container:-ms-fullscreen {
                                                    background: black;
                                                    width: 100vw !important;
                                                    height: 100vh !important;
                                                  }
                                                  
                                                  .video-container:-webkit-full-screen .video-js,
                                                  .video-container:-moz-full-screen .video-js,
                                                  .video-container:-ms-fullscreen .video-js {
                                                    width: 100vw !important;
                                                    height: 100vh !important;
                                                  }
                                                  
                                                  /* Watermark visibility styles removed as per requirements */
                                                  
                                                  /* Prevent developer tools manipulation */
                                                  .video-container * {
                                                    -webkit-touch-callout: none !important;
                                                    -webkit-user-select: none !important;
                                                    -khtml-user-select: none !important;
                                                    -moz-user-select: none !important;
                                                    -ms-user-select: none !important;
                                                    user-select: none !important;
                                                  }
                                                  
                                                  /* Disable right-click context menu */
                                                  .video-container {
                                                    -webkit-touch-callout: none;
                                                    -webkit-user-select: none;
                                                    -khtml-user-select: none;
                                                    -moz-user-select: none;
                                                    -ms-user-select: none;
                                                    user-select: none;
                                                  }
                                                  
                                                  /* Anti-tampering protection styles removed as per requirements */
                                                `}</style>
                                              </div>
                                            ) : (
                                              <div style={{
                                                width: '100%',
                                                height: '400px',
                                                backgroundColor: '#333',
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                color: '#ccc',
                                                borderRadius: '8px',
                                                flexDirection: 'column',
                                                padding: '20px'
                                              }}>
                                                <div style={{ textAlign: 'center' }}>
                                                  <div style={{ fontSize: '24px', marginBottom: '10px' }}>⚠️</div>
                                                  <div style={{ fontSize: '16px', marginBottom: '10px' }}>Video not available</div>
                                                  <div style={{ fontSize: '14px', color: '#888' }}>
                                                    {videoErrors[each.quid] || "Please try refreshing the page or contact support if the issue persists"}
                                                  </div>
                                                </div>
                                              </div>
                                            )}
                                          </td>
                                        </tr>
                                      )}
                                    </tbody>
                                  );
                                })}
                              </table>
                            </AccordionDetails>
                          </Accordion>
                        ) : (
                          <table className="table">
                            <thead>
                              <tr>
                                <th className="tablehead">
                                  {isVideosPackage
                                    ? "Video"
                                    : isPDFPackage
                                      ? "PDF"
                                      : "Exam"}{" "}
                                  Name
                                </th>
                                <th className="tablehead">Action</th>
                              </tr>
                            </thead>
                            {data[day].map((each, index) => {
                              const isNormalPackage = each.isNormalPackage === "1";
                              const isVideosPackage = each.isNormalPackage === "3";
                              const isPDFPackage = !isNormalPackage && !isVideosPackage;

                              return (
                                <tbody key={`quizname${each.quiz_name}`}>
                                  <tr>
                                    <td className="tablehead">
                                      <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                                        {isNormalPackage ? (
                                          <QuizIcon style={{ color: '#4CAF50', fontSize: '20px' }} />
                                        ) : isVideosPackage ? (
                                          <VideoLibraryIcon style={{ color: '#2196F3', fontSize: '20px' }} />
                                        ) : (
                                          <PictureAsPdfIcon style={{ color: '#FF5722', fontSize: '20px' }} />
                                        )}
                                        <p dangerouslySetInnerHTML={{ __html: each.quiz_name }}></p>
                                      </div>
                                    </td>
                                    <td className="tablehead">
                                      <div>
                                        {each.upcoming === 0 &&
                                          each.expired === 0 ? (
                                          !isPDFPackage ? (
                                            <Link
                                              to={`/exam-details/${window.btoa(
                                                each.quid +
                                                "$$_" +
                                                allowNegativeMarking +
                                                "$$_" +
                                                only_for_practice
                                              )}`}
                                              className="linkto"
                                            >
                                              <div className="attemptbtn2" style={{
                                                borderColor: '#4CAF50',
                                                color: '#4CAF50',
                                                '&:hover': {
                                                  backgroundColor: '#4CAF50',
                                                  color: 'white'
                                                }
                                              }}>
                                                Attempt
                                              </div>
                                            </Link>
                                          ) : (
                                            <div>
                                              <div
                                                className="attemptbtn2"
                                                                                                  onClick={() => {
                                                    this.handlePDFNotesClick(each.description, each.quiz_name, isVideosPackage, each.quid, isPDFPackage);
                                                  }}
                                                style={{
                                                  cursor: loadingVideos[each.quid] ? 'wait' : 'pointer',
                                                  opacity: loadingVideos[each.quid] ? 0.7 : 1,
                                                  borderColor: isVideosPackage ? '#2196F3' :
                                                               (isPDFPackage && this.isEncryptedNotes(each.description)) ? '#9C27B0' : '#FF5722',
                                                  color: isVideosPackage ? '#2196F3' :
                                                         (isPDFPackage && this.isEncryptedNotes(each.description)) ? '#9C27B0' : '#FF5722',
                                                  '&:hover': {
                                                    backgroundColor: isVideosPackage ? '#2196F3' :
                                                                     (isPDFPackage && this.isEncryptedNotes(each.description)) ? '#9C27B0' : '#FF5722',
                                                    color: 'white'
                                                  }
                                                }}
                                                disabled={loadingVideos[each.quid]}
                                              >
                                                {isVideosPackage && !each.description.includes('http')
                                                  ? loadingVideos[each.quid]
                                                    ? "Loading Video..."
                                                    : videoErrors[each.quid]
                                                      ? "Retry Video"
                                                      : (playingVideos[each.quid] ? "Hide Video" : "Watch Video")
                                                  : isVideosPackage
                                                    ? "Watch Video"
                                                    : (isPDFPackage && this.isEncryptedNotes(each.description))
                                                      ? "Read Notes"
                                                      : "Download Document"}
                                              </div>
                                              {videoErrors[each.quid] && (
                                                <div style={{
                                                  color: 'red',
                                                  fontSize: '12px',
                                                  marginTop: '5px',
                                                  textAlign: 'center'
                                                }}>
                                                  {videoErrors[each.quid]}
                                                </div>
                                              )}
                                            </div>
                                          )
                                        ) : each.expired === 0 ? (
                                          <div>
                                            <div className="attemptbtn2">
                                              Will Update At {each.startdate}
                                            </div>
                                          </div>
                                        ) : (
                                          <div>
                                            <div
                                              className="attemptbtn2"
                                              style={{
                                                background: "grey",
                                                cursor: "none",
                                              }}
                                            >
                                              Expired
                                            </div>
                                          </div>
                                        )}
                                      </div>
                                    </td>
                                  </tr>
                                  {isVideosPackage && !each.description.includes('http') && playingVideos[each.quid] && (
                                    <tr>
                                      <td colSpan={2} style={{ padding: '15px', backgroundColor: '#333' }}>
                                        <div style={{
                                          display: 'flex',
                                          justifyContent: 'space-between',
                                          alignItems: 'center',
                                          marginBottom: '10px'
                                        }}>
                                          <h4 style={{ color: 'white', margin: '0', fontSize: '16px' }}>
                                            {each.quiz_name.replace(/<[^>]*>/g, '')}
                                          </h4>
                                          <div style={{
                                            display: 'flex',
                                            alignItems: 'center',
                                            fontSize: '12px',
                                            color: '#4CAF50'
                                          }}>
                                            <span style={{ marginRight: '5px' }}>🔒</span>
                                            <span>Secure Access</span>
                                          </div>
                                        </div>
                                        {this.getSecureVideoUrl(each.description) ? (
                                          <div
                                            style={{
                                              position: 'relative',
                                              width: '100%',
                                              height: '400px',
                                              backgroundColor: '#000',
                                              borderRadius: '8px',
                                              overflow: 'hidden'
                                            }}
                                            className="video-container"
                                          >
                                            <video
                                              ref={(ref) => {
                                                this[`originalVideoRef_${each.description}`] = ref;
                                                if (ref) {
                                                  // Initialize VideoJS player
                                                  setTimeout(() => {
                                                    const videoUrl = this.getSecureVideoUrl(each.description);
                                                    if (videoUrl) {
                                                      const player = this.initializeVideoPlayer(ref, each.description, {
                                                        autoplay: false,
                                                        muted: false,
                                                        controls: true,
                                                        sources: [{
                                                          src: videoUrl,
                                                          type: videoUrl.includes('.m3u8') ? 'application/x-mpegURL' : 'video/mp4'
                                                        }]
                                                      });

                                                      if (player) {
                                                        player.on('error', () => {
                                                          this.setState(prevState => ({
                                                            videoErrors: {
                                                              ...prevState.videoErrors,
                                                              [each.quid]: "Failed to load video player. Please try refreshing the page."
                                                            }
                                                          }));
                                                        });
                                                      }
                                                    }
                                                  }, 100);
                                                }
                                              }}
                                              data-video-id={each.description}
                                              data-video-type="original"
                                              className="video-js vjs-default-skin"
                                              style={{
                                                width: '100%',
                                                height: '100%',
                                                position: 'absolute',
                                                top: 0,
                                                left: 0,
                                              }}
                                              onContextMenu={(e) => e.preventDefault()}
                                            />
                                            
                                            {/* Custom Fullscreen Button */}
                                            <button
                                              onClick={() => this.enterFullscreen(each.description)}
                                              style={{
                                                position: 'absolute',
                                                bottom: '15px',
                                                right: '15px',
                                                backgroundColor: 'rgba(0, 0, 0, 0.7)',
                                                color: 'white',
                                                border: 'none',
                                                borderRadius: '6px',
                                                padding: '8px 12px',
                                                cursor: 'pointer',
                                                fontSize: '14px',
                                                fontWeight: 'bold',
                                                zIndex: 1000,
                                                display: 'flex',
                                                alignItems: 'center',
                                                gap: '4px',
                                                transition: 'all 0.2s ease',
                                                userSelect: 'none'
                                              }}
                                              onMouseEnter={(e) => {
                                                e.target.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
                                                e.target.style.transform = 'scale(1.05)';
                                              }}
                                              onMouseLeave={(e) => {
                                                e.target.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
                                                e.target.style.transform = 'scale(1)';
                                              }}
                                            >
                                              <span>⛶</span>
                                              <span>Fullscreen</span>
                                            </button>

                                            {/* Video watermarks removed as per requirements */}
                                            <style>{`
                                            .video-container {
                                              position: relative !important;
                                            }
                                            
                                            .video-container:fullscreen {
                                              background: black;
                                              width: 100vw !important;
                                              height: 100vh !important;
                                            }
                                            
                                            .video-container:fullscreen .video-js {
                                              width: 100vw !important;
                                              height: 100vh !important;
                                            }
                                            
                                            /* Ensure watermark stays visible in fullscreen */
                                            .video-container:fullscreen div[style*="position: absolute"] {
                                              z-index: 2147483647 !important;
                                              font-size: 32px !important;
                                            }
                                            
                                            /* Additional fullscreen selectors for different browsers */
                                            .video-container:-webkit-full-screen,
                                            .video-container:-moz-full-screen,
                                            .video-container:-ms-fullscreen {
                                              background: black;
                                              width: 100vw !important;
                                              height: 100vh !important;
                                            }
                                            
                                            .video-container:-webkit-full-screen .video-js,
                                            .video-container:-moz-full-screen .video-js,
                                            .video-container:-ms-fullscreen .video-js {
                                              width: 100vw !important;
                                              height: 100vh !important;
                                            }
                                            
                                            /* Prevent video overlay manipulation */
                                            .video-js::before,
                                            .video-js::after {
                                              content: none !important;
                                            }

                                            /* Additional security measures for VideoJS */
                                            .video-js {
                                              -webkit-user-select: none !important;
                                              -moz-user-select: none !important;
                                              -ms-user-select: none !important;
                                              user-select: none !important;
                                            }
                                            
                                            /* Watermark positioning styles removed as per requirements */
                                            
                                            :-webkit-full-screen div[style*="position: absolute"],
                                            :-moz-full-screen div[style*="position: absolute"],
                                            :-ms-fullscreen div[style*="position: absolute"] {
                                              position: fixed !important;
                                              z-index: 2147483647 !important;
                                            }
                                          `}</style>
                                          </div>
                                        ) : (
                                          <div style={{
                                            width: '100%',
                                            height: '400px',
                                            backgroundColor: '#333',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            color: '#ccc',
                                            borderRadius: '8px',
                                            flexDirection: 'column',
                                            padding: '20px'
                                          }}>
                                            <div style={{ textAlign: 'center' }}>
                                              <div style={{ fontSize: '24px', marginBottom: '10px' }}>⚠️</div>
                                              <div style={{ fontSize: '16px', marginBottom: '10px' }}>Video not available</div>
                                              <div style={{ fontSize: '14px', color: '#888' }}>
                                                {videoErrors[each.quid] || "Please try refreshing the page or contact support if the issue persists"}
                                              </div>
                                            </div>
                                          </div>
                                        )}
                                      </td>
                                    </tr>
                                  )}
                                </tbody>
                              )
                            })}
                          </table>
                        )}
                      </div>
                    );
                  })
                ) : (
                  <table className="table">
                    <tbody>
                      <tr>
                        <th
                          className="tabledata"
                          style={{
                            width: "100%",
                            fontSize: 18,
                            textAlign: "center",
                          }}
                          colSpan={2}
                        >
                          Your Package has been expired ... <br />
                          Please Pay the Fee to extend your Validity
                          <br />
                          <Link
                            to={`/payumoney/payment/checkout/${guid}/${bucketId}?expired=1`}
                            className="linkto"
                          >
                            <Button
                              variant="contained"
                              className="btn header-btns packagebtn buy buypack"
                            >
                              BUY NOW
                            </Button>
                          </Link>
                        </th>
                      </tr>
                    </tbody>
                  </table>
                )}
              </div>
            </div>
          </div>
        )}
        {isLoading && (
          <div className="loader-main-container">
            <Loader />
          </div>
        )}
        {/* Notes Popup Dialog - Fullscreen */}
        <Dialog 
          open={this.state.notesPopupOpen}
          onClose={this.closeNotesPopup}
          fullScreen
          PaperProps={{
            style: {
              position: 'relative',
              backgroundColor: '#f5f5f5',
              userSelect: 'none',
              WebkitUserSelect: 'none',
              MozUserSelect: 'none',
              msUserSelect: 'none',
              margin: 0,
              maxHeight: 'none',
              height: '100vh',
              width: '100vw',
            }
          }}
        >
          <DialogTitle 
            style={{ 
              backgroundColor: '#333', 
              color: 'white',
              fontSize: window.innerWidth <= 768 ? '16px' : '20px',
              fontWeight: 'bold',
              position: 'sticky',
              top: 0,
              zIndex: 1000,
              userSelect: 'none',
              WebkitUserSelect: 'none',
              MozUserSelect: 'none',
              msUserSelect: 'none',
              padding: window.innerWidth <= 768 ? '12px 16px' : '16px 24px',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
            }}
          >
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: window.innerWidth <= 768 ? '8px' : '10px',
              flex: 1,
              minWidth: 0
            }}>
              <PictureAsPdfIcon style={{ 
                color: '#FF5722',
                fontSize: window.innerWidth <= 768 ? '20px' : '24px'
              }} />
              <span 
                style={{
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  fontSize: window.innerWidth <= 768 ? '14px' : '16px'
                }}
                dangerouslySetInnerHTML={{ __html: this.state.notesTitle.replace(/<[^>]*>/g, '') }}
              ></span>
            </div>
            
            {/* Enhanced Close Button for Mobile */}
            <Button 
              onClick={this.closeNotesPopup}
              style={{
                backgroundColor: '#FF5722',
                color: 'white',
                border: '2px solid #FF5722',
                borderRadius: '6px',
                minWidth: window.innerWidth <= 768 ? '60px' : '80px',
                height: window.innerWidth <= 768 ? '36px' : '40px',
                padding: '0 8px',
                fontSize: window.innerWidth <= 768 ? '14px' : '16px',
                fontWeight: 'bold',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                flexShrink: 0,
              }}
              onMouseEnter={(e) => {
                e.target.style.backgroundColor = '#E64A19';
                e.target.style.transform = 'scale(1.05)';
              }}
              onMouseLeave={(e) => {
                e.target.style.backgroundColor = '#FF5722';
                e.target.style.transform = 'scale(1)';
              }}
            >
              {window.innerWidth <= 768 ? '✕' : 'Close'}
            </Button>
          </DialogTitle>
          <DialogContent 
            style={{ 
              padding: window.innerWidth <= 768 ? '20px 16px' : '40px 60px',
              backgroundColor: '#fff',
              position: 'relative',
              flex: 1,
              overflow: 'auto',
              userSelect: 'none',
              WebkitUserSelect: 'none',
              MozUserSelect: 'none',
              msUserSelect: 'none',
              height: window.innerWidth <= 768 ? 'calc(100vh - 80px)' : 'calc(100vh - 140px)',
            }}
          >
            {/* Content with watermarks */}
            <div 
              style={{ 
                position: 'relative',
                zIndex: 1,
                lineHeight: window.innerWidth <= 768 ? '1.6' : '1.8',
                fontSize: window.innerWidth <= 768 ? '16px' : '18px',
                // fontFamily: 'Arial, sans-serif',
                color: '#333',
                userSelect: 'none',
                WebkitUserSelect: 'none',
                MozUserSelect: 'none',
                msUserSelect: 'none',
                maxWidth: window.innerWidth <= 768 ? '100%' : '1200px',
                margin: '0 auto',
              }}
              dangerouslySetInnerHTML={{ __html: this.state.notesContent }}
            />
            
            {/* Enhanced responsive watermarks for fullscreen security */}
            {this.state.userDetails?.contact_no && (
              <>
                {/* Primary corner watermarks - no overlap */}
                <div
                  style={{
                    position: 'fixed',
                    top: '10%',
                    left: window.innerWidth <= 768 ? '5%' : '8%',
                    transform: 'rotate(-20deg)',
                    color: 'rgba(255, 0, 0, 0.12)',
                    fontSize: window.innerWidth <= 768 ? '32px' : '80px',
                    fontWeight: 'bold',
                    fontFamily: 'monospace',
                    userSelect: 'none',
                    pointerEvents: 'none',
                    zIndex: 2,
                    whiteSpace: 'nowrap',
                  }}
                >
                  {this.state.userDetails.contact_no}
                </div>
                
                <div
                  style={{
                    position: 'fixed',
                    top: '10%',
                    right: window.innerWidth <= 768 ? '5%' : '8%',
                    transform: 'rotate(20deg)',
                    color: 'rgba(0, 0, 255, 0.12)',
                    fontSize: window.innerWidth <= 768 ? '32px' : '80px',
                    fontWeight: 'bold',
                    fontFamily: 'monospace',
                    userSelect: 'none',
                    pointerEvents: 'none',
                    zIndex: 2,
                    whiteSpace: 'nowrap',
                  }}
                >
                  {this.state.userDetails.contact_no}
                </div>
                
                <div
                  style={{
                    position: 'fixed',
                    bottom: '10%',
                    left: window.innerWidth <= 768 ? '5%' : '8%',
                    transform: 'rotate(20deg)',
                    color: 'rgba(0, 255, 0, 0.12)',
                    fontSize: window.innerWidth <= 768 ? '32px' : '80px',
                    fontWeight: 'bold',
                    fontFamily: 'monospace',
                    userSelect: 'none',
                    pointerEvents: 'none',
                    zIndex: 2,
                    whiteSpace: 'nowrap',
                  }}
                >
                  {this.state.userDetails.contact_no}
                </div>
                
                <div
                  style={{
                    position: 'fixed',
                    bottom: '10%',
                    right: window.innerWidth <= 768 ? '5%' : '8%',
                    transform: 'rotate(-20deg)',
                    color: 'rgba(255, 0, 255, 0.12)',
                    fontSize: window.innerWidth <= 768 ? '32px' : '80px',
                    fontWeight: 'bold',
                    fontFamily: 'monospace',
                    userSelect: 'none',
                    pointerEvents: 'none',
                    zIndex: 2,
                    whiteSpace: 'nowrap',
                  }}
                >
                  {this.state.userDetails.contact_no}
                </div>
                
                {/* Center giant watermark - no overlap with corners */}
                <div
                  style={{
                    position: 'fixed',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%) rotate(-45deg)',
                    color: 'rgba(128, 128, 128, 0.08)',
                    fontSize: window.innerWidth <= 768 ? '50px' : '150px',
                    fontWeight: 'bold',
                    fontFamily: 'monospace',
                    userSelect: 'none',
                    pointerEvents: 'none',
                    zIndex: 1,
                    whiteSpace: 'nowrap',
                  }}
                >
                  {this.state.userDetails.contact_no}
                </div>
                
                {/* Middle edge watermarks - clear positioning */}
                <div
                  style={{
                    position: 'fixed',
                    top: '50%',
                    left: window.innerWidth <= 768 ? '2%' : '5%',
                    transform: 'translate(0, -50%) rotate(90deg)',
                    color: 'rgba(255, 165, 0, 0.10)',
                    fontSize: window.innerWidth <= 768 ? '24px' : '60px',
                    fontWeight: 'bold',
                    fontFamily: 'monospace',
                    userSelect: 'none',
                    pointerEvents: 'none',
                    zIndex: 2,
                    whiteSpace: 'nowrap',
                  }}
                >
                  {this.state.userDetails.contact_no}
                </div>
                
                <div
                  style={{
                    position: 'fixed',
                    top: '50%',
                    right: window.innerWidth <= 768 ? '2%' : '5%',
                    transform: 'translate(0, -50%) rotate(-90deg)',
                    color: 'rgba(255, 165, 0, 0.10)',
                    fontSize: window.innerWidth <= 768 ? '24px' : '60px',
                    fontWeight: 'bold',
                    fontFamily: 'monospace',
                    userSelect: 'none',
                    pointerEvents: 'none',
                    zIndex: 2,
                    whiteSpace: 'nowrap',
                  }}
                >
                  {this.state.userDetails.contact_no}
                </div>
                
                {/* Top and bottom center watermarks - clear spacing */}
                <div
                  style={{
                    position: 'fixed',
                    top: '25%',
                    left: '50%',
                    transform: 'translate(-50%, -50%) rotate(-15deg)',
                    color: 'rgba(255, 100, 100, 0.10)',
                    fontSize: window.innerWidth <= 768 ? '28px' : '70px',
                    fontWeight: 'bold',
                    fontFamily: 'monospace',
                    userSelect: 'none',
                    pointerEvents: 'none',
                    zIndex: 2,
                    whiteSpace: 'nowrap',
                  }}
                >
                  {this.state.userDetails.contact_no}
                </div>
                
                <div
                  style={{
                    position: 'fixed',
                    top: '75%',
                    left: '50%',
                    transform: 'translate(-50%, -50%) rotate(15deg)',
                    color: 'rgba(100, 100, 255, 0.10)',
                    fontSize: window.innerWidth <= 768 ? '28px' : '70px',
                    fontWeight: 'bold',
                    fontFamily: 'monospace',
                    userSelect: 'none',
                    pointerEvents: 'none',
                    zIndex: 2,
                    whiteSpace: 'nowrap',
                  }}
                >
                  {this.state.userDetails.contact_no}
                </div>
                
                {/* Small corner security badges */}
                
                
              </>
            )}
          </DialogContent>
          <DialogActions 
            style={{ 
              backgroundColor: '#f5f5f5', 
              padding: window.innerWidth <= 768 ? '16px' : '20px',
              justifyContent: 'center',
              gap: '10px',
              borderTop: '1px solid #ddd'
            }}
          >
            <Button 
              onClick={this.downloadNotesAsPdf}
              style={{
                backgroundColor: '#4CAF50',
                color: 'white',
                border: '2px solid #4CAF50',
                borderRadius: '6px',
                minWidth: window.innerWidth <= 768 ? '120px' : '150px',
                height: window.innerWidth <= 768 ? '40px' : '45px',
                padding: '0 16px',
                fontSize: window.innerWidth <= 768 ? '14px' : '16px',
                fontWeight: 'bold',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                gap: '8px'
              }}
              onMouseEnter={(e) => {
                e.target.style.backgroundColor = '#45a049';
                e.target.style.transform = 'scale(1.05)';
              }}
              onMouseLeave={(e) => {
                e.target.style.backgroundColor = '#4CAF50';
                e.target.style.transform = 'scale(1)';
              }}
            >
              <span>📄</span>
              <span>Download PDF</span>
            </Button>
          </DialogActions>
        </Dialog>

        <div>
          <NotificationContainer />
        </div>
        <style>{`
          /* VideoJS Player Styles */
          .video-js {
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
            user-select: none !important;
            -webkit-touch-callout: none !important;
          }

          .video-js .vjs-tech {
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
            user-select: none !important;
          }

          /* Disable right-click on video */
          .video-js video {
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
            user-select: none !important;
          }

          /* VideoJS fullscreen styles */
          .video-js.vjs-fullscreen {
            width: 100vw !important;
            height: 100vh !important;
          }

          /* Ensure video container maintains aspect ratio */
          .video-container .video-js {
            width: 100% !important;
            height: 100% !important;
          }
          
          /* Enhanced security for fullscreen notes dialog */
          .MuiDialog-paper {
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
            user-select: none !important;
            -webkit-touch-callout: none !important;
            -webkit-app-region: no-drag;
            pointer-events: auto !important;
            overflow: hidden !important;
          }
          
          /* Mobile-specific enhancements */
          @media (max-width: 768px) {
            .MuiDialog-paper {
              margin: 0 !important;
              width: 100vw !important;
              height: 100vh !important;
              max-width: none !important;
              max-height: none !important;
              border-radius: 0 !important;
            }
            
            .MuiDialogTitle-root {
              padding: 12px 16px !important;
              min-height: 60px !important;
            }
            
            .MuiDialogContent-root {
              padding: 20px 16px !important;
              height: calc(100vh - 80px) !important;
              overflow-y: auto !important;
              -webkit-overflow-scrolling: touch !important;
            }
          }
          
          /* Prevent text selection and manipulation in fullscreen dialog */
          .MuiDialog-paper * {
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
            user-select: none !important;
            -webkit-touch-callout: none !important;
            -webkit-app-region: no-drag;
          }
          
          /* Prevent screenshot tools and screen capture */
          .MuiDialog-paper {
            -webkit-region: no-capture;
            -moz-region: no-capture;
            region: no-capture;
          }
          
          /* Anti-tamper protection for watermarks */
          div[style*="position: fixed"] {
            pointer-events: none !important;
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            display: block !important;
            visibility: visible !important;
            z-index: 2147483647 !important;
          }
          
          /* Prevent right-click and drag on watermarks */
          div[style*="position: fixed"]:after,
          div[style*="position: fixed"]:before {
            content: '' !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            pointer-events: none !important;
          }
          
          /* Fullscreen dialog specific styles */
          .MuiDialog-root .MuiDialog-container {
            background-color: rgba(0, 0, 0, 0.8) !important;
          }
          
          /* Disable print media queries */
          @media print {
            .MuiDialog-paper {
              display: none !important;
            }
          }
          
          /* Additional security measures */
          .MuiDialog-paper img,
          .MuiDialog-paper video,
          .MuiDialog-paper canvas {
            -webkit-user-drag: none !important;
            user-drag: none !important;
            -webkit-touch-callout: none !important;
          }
        `}</style>
      </>
    );
  }
}

export default ExamsList;
